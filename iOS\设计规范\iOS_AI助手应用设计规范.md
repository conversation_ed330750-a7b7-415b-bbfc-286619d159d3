# iOS AI助手应用设计规范

> **文档版本**：v2.0
> **最后更新**：2024年12月
> **适用范围**：iOS 15.0+
> **相关文档**：[API集成规范.md](./API集成规范.md) | [界面线框图与导航流程.md](./界面线框图与导航流程.md)

## 项目概述

### 应用名称
YourAI Agent - 极简AI助手

### 设计理念
遵循极简主义设计原则，打造干净、直观、专注的AI对话体验。参考腾讯元宝App和Google Gemini App的优秀设计，同时保持独特的品牌特色。

### 核心价值
- **极简至上**：移除所有非必要元素，专注核心功能
- **直观交互**：零学习成本的用户体验
- **高效沟通**：流畅的AI对话体验
- **iOS原生**：完全遵循iOS设计规范

## 设计系统

### 配色方案

#### 主色调
```
主品牌色：#007AFF (iOS系统蓝)
辅助色：#5856D6 (iOS系统紫)
成功色：#34C759 (iOS系统绿)
警告色：#FF9500 (iOS系统橙)
错误色：#FF3B30 (iOS系统红)
```

#### 中性色
```
文本主色：#000000 (纯黑)
文本次要：#8E8E93 (iOS次要文本)
背景主色：#FFFFFF (纯白)
背景次要：#F2F2F7 (iOS次要背景)
分割线：#C6C6C8 (iOS分割线)
```

#### 深色模式
```
文本主色：#FFFFFF (纯白)
文本次要：#8E8E93 (保持一致)
背景主色：#000000 (纯黑)
背景次要：#1C1C1E (iOS深色次要背景)
分割线：#38383A (iOS深色分割线)
```

### 字体规范

#### 字体族
- **主字体**：SF Pro (iOS系统字体)
- **代码字体**：SF Mono (等宽字体，用于代码显示)

#### 字体大小层级
```
标题1：34pt (Bold)
标题2：28pt (Bold)
标题3：22pt (Semibold)
正文：17pt (Regular)
次要文本：15pt (Regular)
说明文字：13pt (Regular)
小字：11pt (Regular)
```

### 间距系统

#### 基础间距单位
```
基础单位：8pt
常用间距：
- 4pt (0.5x)
- 8pt (1x)
- 16pt (2x)
- 24pt (3x)
- 32pt (4x)
- 48pt (6x)
- 64pt (8x)
```

#### 页面边距
```
水平边距：16pt
垂直边距：24pt
组件间距：16pt
```

### 圆角规范
```
小圆角：4pt (按钮、输入框)
中圆角：8pt (卡片、对话气泡)
大圆角：12pt (大型容器)
完全圆角：50% (头像、圆形按钮)
```

## 核心界面设计

### 1. 启动页 (Launch Screen)

#### 设计要点
- 纯白背景
- 居中显示App图标
- 简洁的品牌标识
- 无多余装饰元素

#### 布局规范
```
背景：纯白色 (#FFFFFF)
图标尺寸：120pt × 120pt
图标位置：垂直居中，水平居中
品牌文字：图标下方24pt，使用标题3字体
```

### 2. 用户认证界面（可选）

#### 2.1 认证设计理念

**可选登录策略**
- 未登录状态：用户可浏览AI对话功能，但不可进行更新操作
- 权限控制：后端接口返回401响应时，自动跳转到登录界面
- 功能限制：未登录用户只能查看内容，无法保存或修改数据
- 数据同步：登录后可同步历史会话和个人设置

#### 2.2 登录界面

**设计原则**
- 简化流程：减少必填字段，提供快速登录选项
- 价值传达：明确说明登录后的增强功能
- 灵活退出：提供"稍后登录"或"继续使用"选项

**布局结构**
```
顶部导航栏
├── 关闭按钮 (左侧) - 返回主界面
├── "登录账户" 标题 (居中)
└── "跳过" 按钮 (右侧) - 继续游客模式

主内容区域
├── 价值说明区域
│   ├── "登录后享受更多功能" (标题3)
│   ├── "• 云端同步会话历史"
│   ├── "• 个性化AI助手设置"
│   └── "• 跨设备无缝体验"
├── 间距 32pt
├── 快速登录区域
│   ├── Apple ID登录按钮
│   ├── 间距 12pt
│   └── Google登录按钮
├── 间距 24pt
├── 分割线 + "或使用邮箱登录" 文字
├── 间距 24pt
├── 输入区域
│   ├── 邮箱输入框
│   ├── 间距 16pt
│   └── 密码输入框
├── 间距 24pt
├── 登录按钮 (主要按钮)
├── 间距 16pt
├── "忘记密码？" (居中链接)
├── 弹性间距
└── 底部区域
    ├── "还没有账户？注册" (居中)
    └── "继续使用游客模式" (次要按钮)
```

#### 2.3 注册界面

**设计原则**
- 最小化注册：只收集必要信息
- 即时验证：实时反馈输入有效性
- 快速完成：支持第三方快速注册

**布局结构**
```
顶部导航栏
├── 返回按钮 (左侧)
├── "创建账户" 标题 (居中)
└── "跳过" 按钮 (右侧)

主内容区域
├── 快速注册区域
│   ├── Apple ID注册按钮
│   ├── 间距 12pt
│   └── Google注册按钮
├── 间距 24pt
├── 分割线 + "或使用邮箱注册" 文字
├── 间距 24pt
├── 输入区域
│   ├── 邮箱输入框
│   ├── 间距 16pt
│   ├── 密码输入框
│   └── 间距 16pt
│   └── 确认密码输入框
├── 间距 24pt
├── 注册按钮 (主要按钮)
├── 间距 16pt
├── 服务条款同意 (复选框 + 文字)
├── 弹性间距
└── 底部区域
    ├── "已有账户？登录" (居中)
    └── "继续使用游客模式" (次要按钮)
```

### 3. 主聊天界面

#### 设计原则
- **内容为王**：最大化对话内容显示区域
- **沉浸体验**：隐藏非必要UI元素
- **快速输入**：优化输入体验
- **便捷切换**：快速切换智能体和访问历史会话

#### 界面结构
```
顶部导航栏 (44pt高度)
├── 左侧：历史会话按钮 (📋)
├── 中央：智能体下拉选择器
│   ├── 当前智能体名称
│   ├── 下拉箭头图标
│   └── 点击展开智能体列表
└── 右侧：个人头像按钮 (👤)

对话内容区域 (弹性高度)
├── 消息列表 (UITableView)
│   ├── 用户消息气泡 (右对齐)
│   ├── AI消息气泡 (左对齐)
│   ├── 系统消息 (居中)
│   └── 加载指示器
└── 底部输入区域

底部输入栏 (自适应高度)
├── 输入框容器
│   ├── 文本输入框 (多行支持)
│   ├── 附件按钮 (左侧)
│   └── 发送按钮 (右侧)
└── 安全区域底部间距
```

#### 新增导航栏组件设计

##### 历史会话按钮 (左侧)
```
图标：📋 (History/List图标)
尺寸：44pt × 44pt
点击区域：最小44pt × 44pt
功能：打开历史会话记录界面
状态：
- 默认：#8E8E93 (次要文本色)
- 点击：#007AFF (主品牌色)
- 有新会话：红点提示
```

##### 智能体下拉选择器 (中央)
```
容器尺寸：弹性宽度 × 44pt
最大宽度：屏幕宽度 - 120pt (左右各60pt)
背景：透明
边框：无

组件结构：
├── 智能体图标 (24pt × 24pt)
├── 智能体名称 (17pt Semibold)
├── 间距 (4pt)
└── 下拉箭头 (16pt × 16pt)

交互状态：
- 默认：#000000 (主文本色)
- 点击：#007AFF (主品牌色)
- 下拉展开：显示智能体列表弹窗
```

##### 个人头像按钮 (右侧)
```
头像尺寸：32pt × 32pt (圆形)
点击区域：44pt × 44pt
功能：打开个人资料界面或登录界面

未登录状态：
- 图标：系统Person图标 + 虚线边框
- 背景色：#F2F2F7 (次要背景色)
- 边框：1pt虚线 #C6C6C8
- 提示：点击后显示登录选项

登录用户状态：
- 默认头像：系统Person图标 + 实线边框
- 自定义头像：用户上传的头像
- 边框：1pt实线 #C6C6C8
- 状态指示：
  • 同步完成：无特殊标识
  • 同步中：右下角旋转动画 (8pt)
  • 离线模式：右下角灰色圆点 (8pt)
```

#### 消息气泡设计
```
用户消息气泡：
- 背景色：#007AFF (主品牌色)
- 文字色：#FFFFFF
- 圆角：12pt
- 最大宽度：屏幕宽度的75%
- 右对齐，右边距16pt

AI消息气泡：
- 背景色：#F2F2F7 (浅灰背景)
- 文字色：#000000
- 圆角：12pt
- 最大宽度：屏幕宽度的80%
- 左对齐，左边距16pt

消息间距：
- 同一发送者连续消息：4pt
- 不同发送者消息：16pt
- 时间戳显示：8pt上下间距
```

### 4. 历史会话记录界面

#### 设计原则
- **时间线组织**：按时间顺序组织会话记录
- **快速预览**：显示会话摘要和关键信息
- **便捷操作**：支持搜索、删除、恢复会话

#### 界面结构
```
顶部导航栏 (44pt高度)
├── 左侧：返回按钮 (←)
├── 中央：标题 "历史会话"
└── 右侧：搜索按钮 (🔍)

搜索栏 (可选显示，44pt高度)
├── 搜索输入框
├── 取消按钮
└── 搜索建议

会话列表区域 (弹性高度)
├── 时间分组标题
│   ├── 今天
│   ├── 昨天
│   ├── 本周
│   ├── 本月
│   └── 更早
├── 会话卡片列表
│   ├── 会话标题/摘要
│   ├── 智能体类型
│   ├── 最后消息时间
│   ├── 消息数量
│   └── 操作按钮
└── 空状态提示

底部操作栏 (可选)
├── 全选按钮
├── 删除按钮
└── 导出按钮
```

#### 会话卡片设计
```
卡片尺寸：全宽-32pt × 80pt
背景色：#FFFFFF (浅色) / #1C1C1E (深色)
圆角：12pt
边框：1pt #F2F2F7 (浅色) / #38383A (深色)
阴影：轻微阴影效果

卡片内容结构：
├── 左侧区域 (60pt宽度)
│   ├── 智能体图标 (32pt × 32pt)
│   └── 消息数量徽章
├── 中间区域 (弹性宽度)
│   ├── 会话标题 (17pt Semibold，最多2行)
│   ├── 最后消息预览 (15pt Regular，1行)
│   └── 时间戳 (13pt Regular)
└── 右侧区域 (44pt宽度)
    ├── 更多操作按钮 (⋯)
    └── 未读消息指示器

交互状态：
- 默认：正常显示
- 点击：进入对应会话
- 长按：显示操作菜单
- 滑动：快速删除/置顶
```

#### 操作菜单设计
```
长按会话卡片显示的操作菜单：
├── 恢复会话 (主要操作)
├── 重命名会话
├── 置顶会话
├── 分享会话
├── 删除会话 (危险操作)
└── 取消

菜单样式：
- 背景：毛玻璃效果
- 圆角：16pt
- 按钮高度：50pt
- 文字：17pt Regular
- 图标：24pt × 24pt
```

### 5. 智能体下拉选择组件

#### 设计原则
- **一键切换**：快速切换不同智能体
- **清晰标识**：明确显示当前选中的智能体
- **流畅动画**：平滑的展开收起动画

#### 组件结构
```
主按钮 (导航栏中央)
├── 智能体图标 (24pt × 24pt)
├── 智能体名称 (17pt Semibold)
├── 间距 (4pt)
└── 下拉箭头 (16pt × 16pt)

下拉列表弹窗
├── 背景遮罩 (半透明黑色)
├── 列表容器
│   ├── 顶部指示器 (小圆角矩形)
│   ├── 智能体选项列表
│   │   ├── Web Search Agent
│   │   ├── Finance Agent
│   │   ├── Agno Assist
│   │   └── Crypto Prediction Agent
│   └── 底部安全区域
└── 手势识别区域
```

#### 智能体选项设计
```
选项卡片尺寸：全宽-32pt × 64pt
背景色：#FFFFFF (浅色) / #2C2C2E (深色)
圆角：12pt
间距：8pt

选项内容结构：
├── 左侧图标区域 (48pt宽度)
│   └── 智能体图标 (32pt × 32pt)
├── 中间信息区域 (弹性宽度)
│   ├── 智能体名称 (17pt Semibold)
│   └── 功能描述 (15pt Regular)
└── 右侧状态区域 (32pt宽度)
    └── 选中指示器 (✓)

选中状态：
- 背景色：#007AFF15 (主色半透明)
- 边框：2pt #007AFF
- 选中图标：#007AFF
- 文字颜色：#007AFF

未选中状态：
- 背景色：默认卡片背景
- 边框：1pt #F2F2F7
- 文字颜色：默认文本色
```

#### 动画效果
```
展开动画：
- 持续时间：0.3s
- 缓动函数：ease-out
- 效果：从上往下滑入 + 淡入

收起动画：
- 持续时间：0.25s
- 缓动函数：ease-in
- 效果：从下往上滑出 + 淡出

选择动画：
- 持续时间：0.2s
- 效果：选中项高亮 + 列表收起
- 主按钮更新：图标和文字平滑切换

箭头旋转：
- 展开：顺时针旋转180°
- 收起：逆时针旋转180°
- 持续时间：0.3s
```

#### 交互行为
```
触发展开：
- 点击主按钮
- 点击下拉箭头
- 键盘快捷键 (如支持)

触发收起：
- 点击背景遮罩
- 选择智能体选项
- 点击主按钮 (再次点击)
- 滑动手势向上

选择智能体：
- 点击智能体选项
- 更新主按钮显示
- 发送切换事件
- 自动收起列表
```

## 组件规范

### 按钮组件

#### 主要按钮 (Primary Button)
```
背景色：#007AFF
文字色：#FFFFFF
字体：17pt Semibold
高度：50pt
圆角：8pt
水平内边距：24pt
最小宽度：120pt
```

#### 次要按钮 (Secondary Button)
```
背景色：透明
边框：1pt #007AFF
文字色：#007AFF
字体：17pt Regular
高度：50pt
圆角：8pt
水平内边距：24pt
```

#### 文本按钮 (Text Button)
```
背景色：透明
文字色：#007AFF
字体：17pt Regular
高度：44pt
水平内边距：16pt
```

### 输入框组件

#### 标准输入框
```
背景色：#F2F2F7
边框：无
文字色：#000000
占位符色：#8E8E93
字体：17pt Regular
高度：44pt
圆角：8pt
水平内边距：16pt
垂直内边距：12pt
```

#### 多行输入框
```
背景色：#F2F2F7
边框：无
文字色：#000000
占位符色：#8E8E93
字体：17pt Regular
最小高度：44pt
最大高度：120pt
圆角：8pt
内边距：16pt
```

### 导航组件

#### 顶部导航栏
```
背景色：#FFFFFF (浅色模式) / #000000 (深色模式)
高度：44pt + 安全区域
标题字体：17pt Semibold
按钮字体：17pt Regular
分割线：1pt #C6C6C8
```

#### 标签栏 (如需要)
```
背景色：#FFFFFF (浅色模式) / #000000 (深色模式)
高度：49pt + 安全区域
图标尺寸：25pt × 25pt
文字字体：10pt Regular
选中色：#007AFF
未选中色：#8E8E93
```

## 交互设计

### 手势交互

#### 基础手势
- **点击**：按钮激活、消息选择
- **长按**：消息操作菜单、快捷操作
- **滑动**：页面导航、消息删除
- **双击**：消息快速回复、文本选择

#### 高级手势
- **下拉刷新**：加载历史消息
- **上滑**：快速滚动到底部
- **捏合缩放**：图片查看（如支持图片）

### 动画效果

#### 页面转场
```
推入动画：0.3s ease-in-out
弹出动画：0.25s ease-in-out
淡入淡出：0.2s ease-in-out
```

#### 微交互动画
```
按钮点击：0.1s scale(0.95)
消息发送：0.3s slide-up + fade-in
输入框聚焦：0.2s scale + border-color
加载指示：1.5s rotation (infinite)
```

### 反馈机制

#### 视觉反馈
- 按钮状态变化
- 消息发送状态指示
- 网络连接状态提示
- 错误状态显示

#### 触觉反馈
- 按钮点击：轻微震动
- 消息发送成功：成功震动
- 错误操作：错误震动
- 长按操作：选择震动

## 用户体验考量



### 性能优化

#### 界面性能
- 60fps流畅滚动
- 快速启动时间 (<2秒)
- 内存使用优化
- 电池消耗控制

#### 网络优化
- 智能重连机制
- 离线消息缓存
- 图片懒加载
- 数据压缩传输

### 错误处理

#### 网络错误
- 连接失败提示
- 自动重试机制
- 离线模式提示
- 数据同步状态

#### 用户错误
- 输入验证提示
- 操作确认对话框
- 撤销操作支持
- 友好的错误信息

## 技术实现指南

### 开发框架
- **UI框架**：SwiftUI (推荐) 或 UIKit
- **网络库**：URLSession + Combine
- **数据存储**：Core Data 或 SQLite
- **图片处理**：Kingfisher (如需要)

### 架构模式
- **MVVM**：Model-View-ViewModel
- **依赖注入**：便于测试和维护
- **响应式编程**：Combine框架
- **模块化设计**：功能模块分离

### API集成概览

iOS应用需要与后端API进行深度集成，支持用户认证、AI对话、会话管理等核心功能。

**主要集成点：**
- **用户认证**：JWT Token双Token机制，支持自动刷新
- **AI Agent对话**：流式响应处理，支持多媒体内容
- **会话管理**：历史记录、重命名、删除等操作
- **用户数据同步**：个人信息、偏好设置的云端同步
- **状态管理**：响应式状态更新和本地持久化

**核心技术特性：**
- **RESTful API**：标准HTTP接口，支持JSON和表单数据
- **流式响应**：基于Server-Sent Events的实时通信
- **认证安全**：Keychain安全存储，Token自动管理
- **错误处理**：完善的重试机制和用户友好提示
- **离线支持**：本地缓存和数据同步策略

**API端点分类：**

| 分类 | 主要功能 | 关键特性 |
|------|----------|----------|
| 认证管理 | 登录、注册、Token管理 | 双Token机制、自动刷新 |
| 用户管理 | 个人信息、偏好设置 | 增量更新、数据验证 |
| Agent管理 | AI对话、会话管理 | 流式响应、多媒体支持 |

> **详细技术规范**：完整的API集成规范请参考 [API集成规范.md](./API集成规范.md)
> **数据模型定义**：包含Message、Agent、Session、User等核心数据结构
> **流式响应处理**：详细的SSE协议实现和错误处理机制
> **状态管理策略**：SwiftUI响应式架构和数据持久化方案




```







### 数据模型概览

应用需要处理多种数据类型，包括用户信息、消息内容、Agent配置、会话记录等。

**核心数据模型：**
- **Message模型**：支持多媒体消息和流式状态
- **Agent模型**：AI智能体配置和能力描述
- **Session模型**：会话管理和历史记录
- **User模型**：用户信息和偏好设置

**设计原则：**
- 数据结构清晰，易于扩展
- 支持本地缓存和离线访问
- 兼容后端API数据格式
- 支持数据迁移和版本升级

> 详细的数据模型规范请参考：[API集成规范.md](./API集成规范.md)

## 总结

本设计规范文档提供了完整的iOS AI助手应用设计指南，遵循极简主义原则和用户中心设计理念。主要特色包括：

### 核心设计特性
- **可选登录设计**：未登录用户可浏览AI对话功能，但更新操作需要登录
- **权限控制**：后端401响应自动跳转登录，确保数据安全
- **现代化技术栈**：采用SwiftUI + SwiftData + CloudKit的现代iOS开发方案
- **极简交互**：专注核心功能，提供零学习成本的直观体验

### 技术实现要点
- 使用SwiftData作为数据持久层，支持本地存储和云端同步
- 采用MVVM + Coordinator架构模式，确保代码可维护性
- 遵循iOS设计规范，深度集成系统特性
- 优化性能和内存管理，提供流畅的60fps体验

开发团队应严格按照此规范进行实现，确保最终产品的一致性和高质量用户体验。所有设计决策都基于iOS平台的最佳实践，同时参考了腾讯元宝App和Google Gemini App的优秀设计理念。
