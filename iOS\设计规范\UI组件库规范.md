# UI组件库规范

## 组件设计原则

### 一致性原则
- 所有组件遵循统一的设计语言
- 保持视觉风格和交互行为的一致性
- 使用标准化的尺寸、间距和颜色

### 可复用性原则
- 组件设计考虑多场景使用
- 提供灵活的配置选项
- 支持主题切换和自定义样式

## 基础组件

### 1. 按钮组件 (Button)

#### 1.1 主要按钮 (Primary Button)
```
设计规格：
- 高度：50pt
- 圆角：8pt
- 字体：SF Pro 17pt Semibold
- 最小宽度：120pt
- 水平内边距：24pt

颜色规范：
- 背景色：#007AFF (浅色) / #0A84FF (深色)
- 文字色：#FFFFFF
- 禁用背景：#C7C7CC
- 禁用文字：#FFFFFF

状态变化：
- 正常：默认样式
- 按下：背景色透明度80%，轻微缩放(0.95)
- 禁用：灰色背景，不可交互
- 加载：显示活动指示器，禁用交互

使用场景：
- 主要操作：登录、注册、发送消息
- 表单提交：确认、保存、下一步
```

#### 1.2 次要按钮 (Secondary Button)
```
设计规格：
- 高度：50pt
- 圆角：8pt
- 边框：1pt
- 字体：SF Pro 17pt Regular
- 水平内边距：24pt

颜色规范：
- 背景色：透明
- 边框色：#007AFF (浅色) / #0A84FF (深色)
- 文字色：#007AFF (浅色) / #0A84FF (深色)
- 禁用边框：#C7C7CC
- 禁用文字：#C7C7CC

使用场景：
- 次要操作：取消、返回、重置
- 可选操作：跳过、稍后、了解更多
```

#### 1.3 文本按钮 (Text Button)
```
设计规格：
- 高度：44pt
- 字体：SF Pro 17pt Regular
- 水平内边距：16pt
- 无背景和边框

颜色规范：
- 文字色：#007AFF (浅色) / #0A84FF (深色)
- 按下色：#007AFF 透明度60%
- 禁用色：#C7C7CC

使用场景：
- 导航链接：忘记密码、注册、帮助
- 内联操作：编辑、删除、查看更多
```

#### 1.4 图标按钮 (Icon Button)
```
设计规格：
- 尺寸：44pt × 44pt (最小点击区域)
- 图标尺寸：24pt × 24pt
- 圆角：22pt (完全圆形)

颜色规范：
- 图标色：#007AFF (浅色) / #0A84FF (深色)
- 背景色：透明或 #F2F2F7
- 按下背景：#E5E5EA

使用场景：
- 导航操作：返回、菜单、设置
- 功能操作：搜索、分享、收藏
```

### 2. 输入组件 (Input)

#### 2.1 文本输入框 (Text Field)
```
设计规格：
- 高度：44pt
- 圆角：8pt
- 字体：SF Pro 17pt Regular
- 水平内边距：16pt
- 垂直内边距：12pt

颜色规范：
- 背景色：#F2F2F7 (浅色) / #1C1C1E (深色)
- 文字色：#000000 (浅色) / #FFFFFF (深色)
- 占位符：#8E8E93
- 边框：无 (正常) / 2pt #007AFF (聚焦)

状态变化：
- 正常：默认样式
- 聚焦：显示蓝色边框，背景稍微变亮
- 错误：红色边框，错误提示文字
- 禁用：灰色背景，不可编辑

使用场景：
- 单行文本：用户名、邮箱、密码
- 数字输入：验证码、手机号
```

#### 2.2 多行文本框 (Text View)
```
设计规格：
- 最小高度：44pt
- 最大高度：120pt (聊天输入框)
- 圆角：8pt
- 字体：SF Pro 17pt Regular
- 内边距：16pt

特殊功能：
- 自动高度调整
- 滚动支持
- 字符计数显示
- 占位符支持

使用场景：
- 聊天消息输入
- 反馈意见输入
- 长文本编辑
```

#### 2.3 搜索框 (Search Field)
```
设计规格：
- 高度：36pt
- 圆角：18pt (完全圆角)
- 字体：SF Pro 17pt Regular
- 左侧图标：搜索图标 16pt
- 右侧图标：清除按钮 (可选)

颜色规范：
- 背景色：#F2F2F7 (浅色) / #1C1C1E (深色)
- 图标色：#8E8E93
- 文字色：#000000 (浅色) / #FFFFFF (深色)

使用场景：
- 全局搜索
- 列表筛选
- 内容查找
```

### 3. 导航组件 (Navigation)

#### 3.1 导航栏 (Navigation Bar)
```
设计规格：
- 高度：44pt + 安全区域
- 背景色：#FFFFFF (浅色) / #000000 (深色)
- 分割线：1pt #C6C6C8 (浅色) / #38383A (深色)

标题样式：
- 字体：SF Pro 17pt Semibold
- 颜色：#000000 (浅色) / #FFFFFF (深色)
- 位置：居中对齐

按钮样式：
- 字体：SF Pro 17pt Regular
- 颜色：#007AFF (浅色) / #0A84FF (深色)
- 最小点击区域：44pt × 44pt

组件结构：
- 左侧：返回按钮或菜单按钮
- 中央：页面标题
- 右侧：操作按钮 (设置、编辑等)
```

#### 3.2 标签栏 (Tab Bar) - 如需要
```
设计规格：
- 高度：49pt + 安全区域
- 背景色：#FFFFFF (浅色) / #000000 (深色)
- 分割线：1pt #C6C6C8 (浅色) / #38383A (深色)

标签项样式：
- 图标尺寸：25pt × 25pt
- 文字字体：SF Pro 10pt Regular
- 选中色：#007AFF (浅色) / #0A84FF (深色)
- 未选中色：#8E8E93

最大标签数：5个 (iOS标准)
```

### 4. 反馈组件 (Feedback)

#### 4.1 消息气泡 (Message Bubble)
```
用户消息气泡：
- 背景色：#007AFF (浅色) / #0A84FF (深色)
- 文字色：#FFFFFF
- 圆角：12pt
- 最大宽度：屏幕宽度75%
- 内边距：12pt 16pt
- 对齐：右对齐，右边距16pt

AI消息气泡：
- 背景色：#F2F2F7 (浅色) / #1C1C1E (深色)
- 文字色：#000000 (浅色) / #FFFFFF (深色)
- 圆角：12pt
- 最大宽度：屏幕宽度80%
- 内边距：12pt 16pt
- 对齐：左对齐，左边距16pt

系统消息：
- 背景色：#E5E5EA (浅色) / #2C2C2E (深色)
- 文字色：#8E8E93
- 圆角：8pt
- 居中对齐
- 字体：SF Pro 13pt Regular
```

#### 4.2 加载指示器 (Loading Indicator)
```
活动指示器：
- 尺寸：20pt × 20pt (小) / 37pt × 37pt (大)
- 颜色：#8E8E93 (默认) / #007AFF (强调)
- 动画：顺时针旋转，1.5秒一周

进度条：
- 高度：4pt
- 圆角：2pt
- 背景色：#E5E5EA (浅色) / #2C2C2E (深色)
- 进度色：#007AFF (浅色) / #0A84FF (深色)

骨架屏：
- 背景色：#F2F2F7 (浅色) / #1C1C1E (深色)
- 动画：从左到右的光泽效果
- 圆角：与实际内容保持一致
```

#### 4.3 提示组件 (Alert & Toast)
```
警告对话框 (Alert)：
- 背景色：#FFFFFF (浅色) / #2C2C2E (深色)
- 圆角：14pt
- 阴影：0 4pt 16pt rgba(0,0,0,0.1)
- 最大宽度：270pt
- 内边距：24pt

标题样式：
- 字体：SF Pro 17pt Semibold
- 颜色：#000000 (浅色) / #FFFFFF (深色)
- 居中对齐

内容样式：
- 字体：SF Pro 13pt Regular
- 颜色：#000000 (浅色) / #FFFFFF (深色)
- 居中对齐

按钮样式：
- 高度：44pt
- 字体：SF Pro 17pt Regular
- 分割线：1pt #C6C6C8

Toast提示：
- 背景色：#000000 透明度80%
- 文字色：#FFFFFF
- 圆角：8pt
- 最大宽度：屏幕宽度-32pt
- 内边距：12pt 16pt
- 位置：屏幕底部，距离安全区域16pt
- 动画：淡入淡出，自动消失(3秒)
```

### 5. 列表组件 (List)

#### 5.1 基础列表项 (List Item)
```
设计规格：
- 最小高度：44pt
- 水平内边距：16pt
- 垂直内边距：12pt
- 分割线：1pt #C6C6C8，左边距16pt

内容结构：
- 左侧：图标 (可选) 24pt × 24pt
- 中央：主标题 + 副标题 (可选)
- 右侧：辅助信息 + 箭头 (可选)

文字样式：
- 主标题：SF Pro 17pt Regular
- 副标题：SF Pro 15pt Regular，#8E8E93
- 辅助信息：SF Pro 15pt Regular，#8E8E93
```

#### 5.2 设置列表项 (Settings Item)
```
开关项：
- 左侧：图标 + 标题
- 右侧：UISwitch (31pt × 51pt)

选择项：
- 左侧：图标 + 标题
- 右侧：当前值 + 箭头

操作项：
- 左侧：图标 + 标题
- 右侧：箭头
- 点击：导航到下级页面

危险操作：
- 文字色：#FF3B30 (红色)
- 无图标和箭头
- 居中对齐
```

### 6. 卡片组件 (Card)

#### 6.1 基础卡片
```
设计规格：
- 背景色：#FFFFFF (浅色) / #1C1C1E (深色)
- 圆角：12pt
- 阴影：0 2pt 8pt rgba(0,0,0,0.1) (仅浅色模式)
- 边框：1pt #E5E5EA (深色模式)
- 内边距：16pt

使用场景：
- Agent选择卡片
- 功能介绍卡片
- 信息展示卡片
```

#### 6.2 Agent卡片
```
结构布局：
- 顶部：图标 + 名称
- 中部：描述文字
- 底部：状态指示 (选中/未选中)

图标样式：
- 尺寸：32pt × 32pt
- 圆角：8pt
- 背景色：渐变或纯色

名称样式：
- 字体：SF Pro 17pt Semibold
- 颜色：#000000 (浅色) / #FFFFFF (深色)

描述样式：
- 字体：SF Pro 15pt Regular
- 颜色：#8E8E93
- 行数：最多2行，超出显示省略号

选中状态：
- 边框：2pt #007AFF
- 右上角：勾选图标
```

## 动画规范

### 基础动画
```
页面转场：
- 推入：0.3s ease-in-out
- 弹出：0.25s ease-in-out
- 淡入淡出：0.2s ease-in-out

微交互：
- 按钮点击：0.1s scale(0.95)
- 开关切换：0.2s ease-in-out
- 列表项选中：0.15s ease-out

加载动画：
- 旋转：1.5s linear infinite
- 脉冲：1.0s ease-in-out infinite alternate
- 骨架屏：1.2s ease-in-out infinite
```

### 手势动画
```
滑动操作：
- 阻尼系数：0.8
- 回弹时间：0.3s
- 最大偏移：屏幕宽度30%

长按反馈：
- 延迟：0.5s
- 缩放：scale(1.05)
- 阴影增强：0 4pt 16pt rgba(0,0,0,0.2)
```

## 响应式适配

### 屏幕尺寸适配
```
iPhone SE (375pt宽)：
- 减少水平内边距至12pt
- 按钮最小宽度调整至100pt
- 字体大小保持不变

iPhone 标准 (390pt宽)：
- 标准内边距16pt
- 标准按钮尺寸
- 标准字体大小

iPhone Plus/Max (428pt宽)：
- 增加内容显示密度
- 保持标准内边距
- 可适当增加字体大小
```



## 主题系统

### 浅色主题
```
背景层级：
- 主背景：#FFFFFF
- 次要背景：#F2F2F7
- 三级背景：#FFFFFF

文本层级：
- 主要文本：#000000
- 次要文本：#8E8E93
- 禁用文本：#C7C7CC

边框分割：
- 主要边框：#C6C6C8
- 次要边框：#E5E5EA
```

### 深色主题
```
背景层级：
- 主背景：#000000
- 次要背景：#1C1C1E
- 三级背景：#2C2C2E

文本层级：
- 主要文本：#FFFFFF
- 次要文本：#8E8E93
- 禁用文本：#48484A

边框分割：
- 主要边框：#38383A
- 次要边框：#48484A
```

这份UI组件库规范提供了完整的组件设计标准，确保整个应用的视觉一致性和用户体验的连贯性。开发团队可以基于这些规范创建可复用的组件库，提高开发效率和代码质量。
