---
type: "agent_requested"
description: "前端开发规范"
---
# 前端开发规范 

## 项目结构 ([frontend/](mdc:frontend))
  - `src/`
    - `components/`: Vue组件
      - `layout/`: 布局组件
        - [TheHeader.vue](mdc:frontend/src/components/layout/TheHeader.vue): 应用头部
        - [TheFooter.vue](mdc:frontend/src/components/layout/TheFooter.vue): 应用底部
      - `playground/`: 游乐场相关组件
        - `ChatArea/`: 聊天区域组件
          - [ChatArea.vue](mdc:frontend/src/components/playground/ChatArea/ChatArea.vue) - 主聊天区域
          - [ChatInput.vue](mdc:frontend/src/components/playground/ChatArea/ChatInput.vue) - 输入框
          - [MessageBubble.vue](mdc:frontend/src/components/playground/ChatArea/MessageBubble.vue) - 消息气泡
          - [MarkdownRenderer.vue](mdc:frontend/src/components/playground/ChatArea/MarkdownRenderer.vue) - Markdown渲染器
        - `Sidebar/`: 侧边栏组件
          - [PlaygroundSidebar.vue](mdc:frontend/src/components/playground/Sidebar/PlaygroundSidebar.vue) - 游乐场侧边栏
          - [AgentSelector.vue](mdc:frontend/src/components/playground/Sidebar/AgentSelector.vue) - 代理选择器
          - [SessionsList.vue](mdc:frontend/src/components/playground/Sidebar/SessionsList.vue) - 会话列表
      - [LanguageSwitcher.vue](mdc:frontend/src/components/LanguageSwitcher.vue): 语言切换器
    - `views/`: 页面视图
      - `agent/`: AI代理相关页面
        - [AgentMarketplace.vue](mdc:frontend/src/views/agent/AgentMarketplace.vue) - 代理市场
      - `auth/`: 认证页面
        - [PageLogin.vue](mdc:frontend/src/views/auth/PageLogin.vue) - 登录页面
        - [PageRegister.vue](mdc:frontend/src/views/auth/PageRegister.vue) - 注册页面
      - `playground/`: 游乐场主页面
        - [PlaygroundPage.vue](mdc:frontend/src/views/playground/PlaygroundPage.vue) - 游乐场主页
      - `user/`: 用户管理页面
        - [PageMe.vue](mdc:frontend/src/views/user/PageMe.vue) - 个人资料页面
      - `static/`: 静态页面
        - [About.vue](mdc:frontend/src/views/static/About.vue) - 关于页面
      - [Home.vue](mdc:frontend/src/views/Home.vue) - 首页
      - [Pricing.vue](mdc:frontend/src/views/Pricing.vue) - 价格页面
    - `stores/`: Pinia状态管理
        - [playground.ts](mdc:frontend/src/stores/playground.ts) - 游乐场状态
        - [user.ts](mdc:frontend/src/stores/user.ts) - 用户状态
        - [theme.ts](mdc:frontend/src/stores/theme.ts) - 主题状态
        - [settings.ts](mdc:frontend/src/stores/settings.ts) - 设置状态
    - `types/`: TypeScript类型定义
        - [playground.ts](mdc:frontend/src/types/playground.ts) - 游乐场类型
    - `apis/`: API调用封装
    - `router/`: 路由配置
    - `i18n/`: 国际化配置
    - `utils/`: 工具函数
        - [request.ts](mdc:frontend/src/utils/request.ts) - HTTP请求封装
        - [playground.ts](mdc:frontend/src/utils/playground.ts) - 游乐场工具
        - [tokenUtils.ts](mdc:frontend/src/utils/tokenUtils.ts) - Token处理
    - `composables/`: 组合式API
        - [useStreamHandler.ts](mdc:frontend/src/composables/useStreamHandler.ts) - 流式数据处理
        - [useMessageActions.ts](mdc:frontend/src/composables/useMessageActions.ts) - 消息操作
        - [useTheme.js](mdc:frontend/src/composables/useTheme.js) - 主题管理
  - [package.json](mdc:frontend/package.json): 依赖与脚本
  - [vite.config.ts](mdc:frontend/vite.config.ts): Vite构建配置
  - [tailwind.config.js](mdc:frontend/tailwind.config.js): Tailwind CSS配置
  - [tsconfig.json](mdc:frontend/tsconfig.json): TypeScript配置

## 开发最佳实践
- 组件化开发:
  - 组件命名: 文件名使用 `PascalCase`, 标签使用 `kebab-case`
  - 组织结构: 复杂组件拆分为子组件，共享组件与页面特定组件分离
- TypeScript规范:
  - 新代码必须使用TypeScript
  - 类型定义集中在 `src/types/` 目录
- 样式方案:
  - 优先使用Tailwind CSS原子类
  - 必要时使用`<style scoped>`进行样式隔离
- API请求:
  - 统一封装在 `src/apis/` 目录
  - 实现统一的请求/响应拦截与错误处理
- 国际化:
  - 所有面向用户的文本必须支持国际化
  - 语言文件存放于 `src/locales/`
- 代码风格:
  - 遵循Vue 3 Composition API最佳实践
  - 保持代码简洁、可读、可维护

## 开发流程与工具
- 依赖安装: `yarn install`
- 本地开发: `yarn dev`
- 生产构建: `yarn build`
- 类型检查: `yarn type-check`
- 代码检查: `yarn lint`

## 注意事项
- 服务管理: 禁止主动启动开发服务器 (`yarn dev`)
- 用户交互: 需要时使用`mcp-feedback-enhanced`工具与用户沟通
- 体验优先: 注重用户体验和界面响应性
