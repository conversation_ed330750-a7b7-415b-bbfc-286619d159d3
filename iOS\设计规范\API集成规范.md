# API集成规范

> **文档版本**：v2.1
> **最后更新**：2024年12月
> **适用范围**：iOS 15.0+
> **相关文档**：[iOS_AI助手应用设计规范.md](./iOS_AI助手应用设计规范.md)

## 概述

本文档定义了iOS AI助手应用与后端API的集成标准，基于YourAgen项目的实际API架构，为开发团队提供完整的API集成指导。

**文档目标：**
- 提供标准化的API集成规范
- 定义完整的数据模型和接口协议
- 指导流式响应和状态管理的实现
- 确保应用的安全性和性能优化

**使用说明：**
- 开发团队应严格遵循本文档的技术规范
- 所有API调用必须实现错误处理和重试机制
- 数据模型定义为强制性标准，不得随意修改
- 流式响应处理需要特别关注内存管理和性能优化

## API架构设计

### 基础配置要求
- 支持生产环境和开发环境切换
- 统一的API前缀：`/api/v1`
- 请求超时设置：30秒
- 支持长连接流式响应：300秒

### 核心API端点分类

| 分类 | 端点前缀 | 说明 |
|------|----------|------|
| 认证管理 | `/api/v1/auth/` | 用户登录、注册、Token管理 |
| 用户管理 | `/api/v1/users/` | 用户信息获取和更新 |
| Agent管理 | `/api/v1/agents/` | AI智能体相关操作 |
| 系统监控 | `/health`, `/api/v1/agents/status` | 健康检查和状态监控 |

## 认证机制设计

### Token管理策略
- 使用JWT Bearer Token认证
- 支持Access Token和Refresh Token双Token机制
- Token自动刷新和失效处理
- 本地安全存储（Keychain）

### 认证流程
1. 用户登录获取Token对
2. 每次API请求携带Access Token
3. Token过期时自动使用Refresh Token刷新
4. 刷新失败时引导用户重新登录

## 数据传输规范

### 请求格式
- 认证接口：`application/x-www-form-urlencoded`
- 其他接口：`application/json`
- 文件上传：`multipart/form-data`
- 流式请求：支持Server-Sent Events

### 响应格式
- 标准JSON响应结构
- 统一的错误码和错误信息
- 分页数据包含total、page、size、pages字段
- 流式响应支持事件类型标识

## 核心API接口规范

### 认证接口

| 接口 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 用户登录 | POST | `/api/v1/auth/login` | 表单格式，返回Token对 |
| 用户注册 | POST | `/api/v1/auth/register` | JSON格式，包含确认密码 |
| Token刷新 | POST | `/api/v1/auth/refresh` | 使用Refresh Token获取新Token |
| 用户登出 | POST | `/api/v1/auth/logout` | 使当前Token失效 |

### 用户管理接口

| 接口 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 获取用户信息 | GET | `/api/v1/users/me` | 需要认证，返回完整用户信息 |
| 更新用户信息 | PATCH | `/api/v1/users/me` | 需要认证，支持部分字段更新 |

### Agent管理接口

| 接口 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 获取Agent列表 | GET | `/api/v1/agents` | 返回可用的AI智能体列表 |
| 运行Agent | POST | `/api/v1/agents/{agent_id}/runs` | 流式响应，支持文件上传 |
| 获取会话列表 | GET | `/api/v1/agents/{agent_id}/sessions` | 分页返回历史会话 |
| 获取会话详情 | GET | `/api/v1/agents/{agent_id}/sessions/{session_id}` | 返回特定会话信息 |
| 重命名会话 | POST | `/api/v1/agents/{agent_id}/sessions/{session_id}/rename` | 修改会话标题 |
| 删除会话 | DELETE | `/api/v1/agents/{agent_id}/sessions/{session_id}` | 删除指定会话 |



## 数据模型设计规范

### 消息数据模型

**Message结构设计：**
- 支持多种消息角色：用户(user)、助手(assistant)、系统(system)
- 包含多媒体内容：文本、图片、音频、视频
- 支持工具调用和函数执行结果
- 包含性能指标：Token使用量、响应时间等
- 支持流式传输状态管理

**核心字段规范：**

| 字段名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| id | String | ✓ | 消息唯一标识符 |
| role | MessageRole | ✓ | 消息角色（user/assistant/system） |
| content | String | ✓ | 消息文本内容 |
| toolCalls | [ToolCall]? | - | 工具调用信息 |
| images | [String]? | - | 图片URL数组 |
| audio | [String]? | - | 音频URL数组 |
| videos | [String]? | - | 视频URL数组 |
| responseAudio | String? | - | 响应音频URL |
| metrics | MessageMetrics? | - | 性能指标数据 |
| createdAt | TimeInterval | ✓ | 创建时间戳 |
| isStreaming | Bool | ✓ | 是否正在流式传输 |
| streamingError | Bool | ✓ | 流式传输是否出错 |

### Agent数据模型

**Agent结构设计：**
- 支持多种AI模型和提供商
- 包含完整的Agent配置信息
- 支持图标映射和UI展示
- 支持存储配置和功能扩展

**核心字段规范：**

| 字段名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| id | String | ✓ | Agent唯一标识符 |
| agentId | String | ✓ | Agent业务ID |
| name | String | ✓ | Agent显示名称 |
| description | String | ✓ | Agent功能描述 |
| model | AgentModel | ✓ | 模型配置信息 |
| storage | Bool? | - | 是否支持存储 |

**Agent类型映射：**

| Agent ID | 显示名称 | 图标 | 功能描述 |
|----------|----------|------|----------|
| web_agent | Web Search Agent | globe | 网络搜索和信息查询 |
| finance_agent | Finance Agent | chart.line.uptrend.xyaxis | 金融数据分析 |
| agno_assist | Agno Assist | questionmark.circle | Agno框架指导 |
| crypto_prediction_agent | Crypto Prediction | bitcoinsign.circle | 加密货币市场分析 |

### Session数据模型

**Session结构设计：**
- 支持会话的完整生命周期管理
- 包含会话统计信息和最后消息预览
- 支持时间戳和相对时间显示
- 支持Agent关联和会话标题管理

**核心字段规范：**

| 字段名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| id | String | ✓ | 会话唯一标识符 |
| sessionId | String | ✓ | 会话业务ID |
| title | String | ✓ | 会话标题（默认"新对话"） |
| agentId | String? | - | 关联的Agent ID |
| agentName | String? | - | 关联的Agent名称 |
| messageCount | Int | ✓ | 消息数量（默认0） |
| lastMessage | String? | - | 最后一条消息预览 |
| createdAt | TimeInterval | ✓ | 创建时间戳 |
| updatedAt | TimeInterval | ✓ | 更新时间戳 |

### 用户数据模型

**User结构设计：**
- 包含完整的用户信息字段
- 支持多种显示名称策略
- 支持头像URL和默认头像
- 包含账户状态和权限管理

**核心字段规范：**

| 字段名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| id | String | ✓ | 用户唯一标识符 |
| username | String? | - | 用户名 |
| email | String | ✓ | 邮箱地址 |
| display_name | String? | - | 显示名称 |
| full_name | String? | - | 全名 |
| avatar_url | String? | - | 头像URL |
| bio | String? | - | 个人简介 |
| timezone | String | ✓ | 时区设置 |
| locale | String | ✓ | 语言设置 |
| phone_number | String? | - | 电话号码 |
| is_active | Bool | ✓ | 账户是否激活 |
| is_locked | Bool | ✓ | 账户是否锁定 |
| account_status | String | ✓ | 账户状态 |
| status_reason | String? | - | 状态原因 |
| create_time | String | ✓ | 创建时间 |

## 流式响应技术规范

### 流式传输协议规范

| 协议特性 | 技术规范 | 实现要求 |
|----------|----------|----------|
| 传输协议 | HTTP/1.1 长连接 | 保持连接300秒超时 |
| 数据格式 | JSON Lines (JSONL) | 每行一个完整JSON对象 |
| 编码格式 | UTF-8 | 支持多语言字符 |
| 连接管理 | 自动重连机制 | 断线后3秒内重连，最多重试5次 |
| 缓冲策略 | 逐行解析 | 避免内存积累，实时处理 |

### 流式事件类型详细规范

| 事件类型 | 触发时机 | 数据内容 | UI响应 | 状态变更 |
|----------|----------|----------|--------|----------|
| run_started | Agent开始处理请求 | sessionId, agentId | 显示"正在思考..." | isStreaming = true |
| run_response | Agent生成内容片段 | content, tools | 逐字显示内容 | 更新消息内容 |
| run_completed | Agent完成响应 | 完整消息数据 | 停止加载动画 | isStreaming = false |
| run_error | 处理过程出错 | error信息 | 显示错误提示 | streamingError = true |
| run_stopped | 用户手动停止 | 当前状态 | 保持现有内容 | isStreaming = false |

### StreamResponse数据结构规范

| 字段名 | 数据类型 | 必需性 | 数据来源 | 处理逻辑 |
|--------|----------|--------|----------|----------|
| event | StreamEventType | 必需 | 服务端状态 | 根据事件类型分发处理 |
| sessionId | String? | 可选 | 首次创建 | run_started时保存到本地 |
| content | String? | 可选 | AI生成内容 | 累积拼接到消息内容 |
| tools | [String]? | 可选 | 工具调用结果 | 解析并显示工具执行状态 |
| images | [String]? | 可选 | 图片生成URL | 异步加载并显示图片 |
| audio | [String]? | 可选 | 音频文件URL | 提供播放控件 |
| videos | [String]? | 可选 | 视频文件URL | 内嵌视频播放器 |
| responseAudio | String? | 可选 | TTS音频URL | 自动播放或手动播放 |
| messages | [Message]? | 可选 | 完整对话历史 | run_completed时更新历史 |
| createdAt | TimeInterval | 必需 | 服务端时间戳 | 用于排序和去重 |

### 流式响应处理流程

1. **连接建立阶段：**
   - 发送POST请求到Agent运行端点
   - 设置Accept: text/event-stream头
   - 建立长连接并开始监听数据流

2. **数据接收阶段：**
   - 逐行读取服务端推送的数据
   - 解析每行JSON数据为StreamResponse对象
   - 根据event类型分发到对应处理器

3. **内容更新阶段：**
   - run_response事件：累积拼接content到UI
   - 实现打字机效果的逐字显示
   - 处理多媒体内容的异步加载

4. **状态管理阶段：**
   - 维护流式传输状态标志
   - 处理错误状态和重试逻辑
   - 管理用户交互状态（如停止按钮）

5. **连接关闭阶段：**
   - run_completed或run_error时关闭连接
   - 清理资源和重置状态
   - 保存最终消息到本地存储

### 错误处理和重连机制

| 错误类型 | 检测方式 | 处理策略 | 用户反馈 |
|----------|----------|----------|----------|
| 网络中断 | 连接超时 | 自动重连3次 | 显示"重新连接中..." |
| 数据解析错误 | JSON解析失败 | 跳过错误数据 | 记录日志，继续处理 |
| 服务端错误 | HTTP 5xx状态码 | 停止流式传输 | 显示"服务暂时不可用" |
| 认证失败 | HTTP 401状态码 | 刷新Token重试 | 透明处理或重新登录 |
| 用户取消 | 手动停止操作 | 立即关闭连接 | 保持当前内容状态 |

### 性能优化策略

| 优化方面 | 技术方案 | 实现细节 |
|----------|----------|----------|
| 内存管理 | 流式解析 | 避免缓存完整响应，逐行处理 |
| UI更新频率 | 节流控制 | 限制UI更新频率为60fps |
| 网络优化 | 连接复用 | 同一会话复用HTTP连接 |
| 数据压缩 | GZIP压缩 | 服务端启用压缩传输 |
| 缓存策略 | 智能缓存 | 缓存媒体文件，文本内容实时更新 |

### 多媒体内容流式处理

| 内容类型 | 传输方式 | 处理策略 | 用户体验 |
|----------|----------|----------|----------|
| 文本内容 | 实时流式 | 逐字符显示 | 打字机效果 |
| 图片内容 | URL引用 | 异步加载 | 占位符→加载→显示 |
| 音频内容 | URL引用 | 预加载 | 播放控件+进度条 |
| 视频内容 | URL引用 | 懒加载 | 缩略图→播放器 |
| 工具调用 | 结构化数据 | 实时解析 | 状态指示器+结果展示 |

## 状态管理规范

### 应用状态管理
- 使用SwiftUI的@ObservableObject和@Published
- 支持响应式UI更新
- 状态持久化和恢复
- 错误状态和加载状态管理

### 核心状态类型

| 状态类 | 职责 | 主要属性 |
|--------|------|----------|
| UserData | 用户状态管理 | currentUser, isLoggedIn, token |
| ChatStore | 对话状态管理 | agents, messages, sessions, isStreaming |
| ThemeSettings | 主题状态管理 | darkMode, fontSize, language |
| LanguageManager | 语言状态管理 | currentLanguage, supportedLanguages |

### 状态同步策略
- 本地状态优先，网络状态同步
- 支持离线模式和数据缓存
- 状态变更的响应式更新
- 错误恢复和重试机制

## 实际应用集成指南

### 核心功能实现策略

**Agent管理功能：**
- 应用启动时自动加载可用Agent列表
- 支持Agent切换和状态管理
- 自动选择默认Agent（通常为第一个）
- Agent图标和描述的本地化显示

**消息流式处理：**
- 用户消息立即显示，AI消息流式更新
- 支持消息状态管理（发送中、完成、错误）
- 流式响应的实时UI更新
- 错误处理和重试机制

**会话管理功能：**
- 自动创建和管理会话ID
- 支持会话历史记录和恢复
- 会话标题自动生成和手动修改
- 会话删除和数据清理

**状态同步策略：**
- 使用Combine框架进行响应式编程
- @Published属性自动触发UI更新
- 主线程执行UI相关操作
- 后台线程处理网络请求

**错误处理机制：**
- 网络错误的用户友好提示
- 流式传输中断的恢复处理
- Token过期的自动刷新
- 离线模式的降级处理

**性能优化建议：**
- 消息列表的虚拟化滚动
- 图片和媒体内容的懒加载
- 会话数据的分页加载
- 本地缓存和数据持久化

**用户体验优化：**
- 流式响应的打字机效果
- 加载状态的视觉反馈
- 错误状态的重试机制
- 离线状态的友好提示

**安全性考虑：**
- Token的安全存储（Keychain）
- 敏感数据的加密传输
- 用户隐私数据的保护
- API调用的频率限制

---

**文档版本**：v1.0  
**最后更新**：2024年12月  
**适用范围**：iOS 15.0+
